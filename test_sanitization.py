#!/usr/bin/env python3
"""
Test script to analyze Excel sanitization function and identify corruption issues
"""

import sys
from email_processor import <PERSON>ailPars<PERSON>

def test_sanitization():
    """Test the sanitize_excel_value function with problematic characters"""
    parser = EmailParser()

    # Test cases with characters that commonly cause Excel XML corruption
    test_cases = [
        'Normal text',
        'Text with \x0B vertical tab',
        'Text with \x0C form feed', 
        'Text with \x1C file separator',
        'Text with \x1D group separator',
        'Text with \x1E record separator',
        'Text with \x1F unit separator',
        'Text with \x00 null character',
        'Text with \x01 start of heading',
        'Text with \x02 start of text',
        'Text with \x03 end of text',
        'Text with \x04 end of transmission',
        'Text with \x05 enquiry',
        'Text with \x06 acknowledge',
        'Text with \x07 bell',
        'Text with \x08 backspace',
        'Text with \x0E shift out',
        'Text with \x0F shift in',
        'Text with \x10 data link escape',
        'Text with \x11 device control 1',
        'Text with \x12 device control 2',
        'Text with \x13 device control 3',
        'Text with \x14 device control 4',
        'Text with \x15 negative acknowledge',
        'Text with \x16 synchronous idle',
        'Text with \x17 end of transmission block',
        'Text with \x18 cancel',
        'Text with \x19 end of medium',
        'Text with \x1A substitute',
        'Text with \x1B escape',
        'Formula =SUM(A1:A10)',
        'Formula +A1+B1',
        'Formula -A1-B1',
        'Formula @INDIRECT(A1)',
        'Text with & ampersand',
        'Text with < less than',
        'Text with > greater than',
        'Text with " quote',
        "Text with ' apostrophe",
        'Unicode: 你好世界',
        'Emoji: 😀🎉🚀',
        'Mixed: Hello\x0BWorld\x1F测试',
    ]

    print('Testing sanitize_excel_value function:')
    print('=' * 60)

    issues_found = []
    
    for i, test_case in enumerate(test_cases):
        try:
            sanitized = parser.sanitize_excel_value(test_case)
            
            # Check if sanitization changed anything
            changed = test_case != sanitized
            
            print(f'{i+1:2d}. Input:  {repr(test_case[:50])}')
            print(f'    Output: {repr(sanitized[:50])}')
            print(f'    Changed: {changed}')
            
            # Check for remaining problematic characters
            remaining_issues = []
            for char in sanitized:
                code = ord(char)
                if code < 32 and code not in [9, 10, 13]:
                    remaining_issues.append(f'0x{code:02X}')
            
            if remaining_issues:
                print(f'    WARNING: Still contains problematic chars: {remaining_issues[:5]}')
                issues_found.append(f'Test {i+1}: {remaining_issues}')
            
            print()
            
        except Exception as e:
            print(f'{i+1:2d}. ERROR processing {repr(test_case)}: {e}')
            print()
            issues_found.append(f'Test {i+1}: Exception - {e}')
    
    print('\n' + '=' * 60)
    if issues_found:
        print('SUMMARY - Issues found:')
        for issue in issues_found:
            print(f'  {issue}')
    else:
        print('SUMMARY - No issues found in sanitization function')
    
    return len(issues_found) == 0

if __name__ == '__main__':
    success = test_sanitization()
    sys.exit(0 if success else 1)
