#!/usr/bin/env python3
"""
Detailed compatibility test to identify specific implementation issues
"""

import sys
import os
import tempfile
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_sanitization_access():
    """Test that both classes can access sanitization properly"""
    print("=== TESTING SANITIZATION ACCESS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Test ProcessingThread
        pt = ProcessingThread([], "test.xlsx")
        pt.results = [{'test_field': 'test value with special chars: \x0B\x0C'}]
        
        # Check if it has parser access
        if hasattr(pt, 'parser'):
            print("✓ ProcessingThread has parser attribute")
            try:
                sanitized = pt.parser.sanitize_excel_value('test\x0B')
                print(f"✓ ProcessingThread sanitization works: '{sanitized}'")
            except Exception as e:
                issues.append(f"ProcessingThread sanitization failed: {e}")
        else:
            issues.append("ProcessingThread missing parser attribute")
        
        # Test ThreadedProcessingThread
        tpt = ThreadedProcessingThread([], "test.xlsx")
        tpt.results = [{'test_field': 'test value with special chars: \x0B\x0C'}]
        
        # Check sanitization access in optimized method
        if hasattr(tpt, '_temp_parser'):
            print("✓ ThreadedProcessingThread has _temp_parser")
        else:
            print("⚠️ ThreadedProcessingThread will create _temp_parser on demand")
        
        # Test creating temp parser
        try:
            from email_processor import EmailParser
            temp_parser = EmailParser()
            sanitized = temp_parser.sanitize_excel_value('test\x0B')
            print(f"✓ ThreadedProcessingThread temp parser works: '{sanitized}'")
        except Exception as e:
            issues.append(f"ThreadedProcessingThread temp parser failed: {e}")
        
    except Exception as e:
        issues.append(f"Sanitization access test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_fallback_consistency():
    """Test that fallback implementations are consistent"""
    print("\n=== TESTING FALLBACK CONSISTENCY ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Check ProcessingThread fallback
        pt = ProcessingThread([], "test.xlsx")
        
        # ProcessingThread should call export_to_excel_original
        if hasattr(pt, 'export_to_excel_original'):
            print("✓ ProcessingThread has export_to_excel_original")
        else:
            issues.append("ProcessingThread missing export_to_excel_original")
        
        # Check ThreadedProcessingThread fallback
        tpt = ThreadedProcessingThread([], "test.xlsx")
        
        # ThreadedProcessingThread should have its own implementation
        if hasattr(tpt, '_export_to_excel_openpyxl'):
            print("✓ ThreadedProcessingThread has _export_to_excel_openpyxl")
            
            # But it should NOT call export_to_excel_original (different pattern)
            if hasattr(tpt, 'export_to_excel_original'):
                issues.append("ThreadedProcessingThread has export_to_excel_original (inconsistent pattern)")
            else:
                print("✓ ThreadedProcessingThread uses direct implementation (consistent)")
        else:
            issues.append("ThreadedProcessingThread missing _export_to_excel_openpyxl")
        
    except Exception as e:
        issues.append(f"Fallback consistency test failed: {e}")
    
    return issues

def test_data_validation_methods():
    """Test that data validation methods exist in both classes"""
    print("\n=== TESTING DATA VALIDATION METHODS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        required_methods = ['_validate_results_data_integrity', '_verify_excel_file_integrity']
        
        for method_name in required_methods:
            # Test ProcessingThread
            pt = ProcessingThread([], "test.xlsx")
            if hasattr(pt, method_name):
                print(f"✓ ProcessingThread has {method_name}")
            else:
                issues.append(f"ProcessingThread missing {method_name}")
            
            # Test ThreadedProcessingThread
            tpt = ThreadedProcessingThread([], "test.xlsx")
            if hasattr(tpt, method_name):
                print(f"✓ ThreadedProcessingThread has {method_name}")
            else:
                issues.append(f"ThreadedProcessingThread missing {method_name}")
        
    except Exception as e:
        issues.append(f"Data validation methods test failed: {e}")
    
    return issues

def test_error_handling_paths():
    """Test error handling in different code paths"""
    print("\n=== TESTING ERROR HANDLING PATHS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Test with empty results
        pt = ProcessingThread([], "test.xlsx")
        pt.results = []
        
        # This should not crash
        try:
            # Don't actually call export (would create files), just check method exists
            if callable(getattr(pt, 'export_to_excel')):
                print("✓ ProcessingThread export_to_excel is callable with empty results")
        except Exception as e:
            issues.append(f"ProcessingThread export_to_excel not callable: {e}")
        
        # Test ThreadedProcessingThread
        tpt = ThreadedProcessingThread([], "test.xlsx")
        tpt.results = []
        
        try:
            if callable(getattr(tpt, 'export_to_excel')):
                print("✓ ThreadedProcessingThread export_to_excel is callable with empty results")
        except Exception as e:
            issues.append(f"ThreadedProcessingThread export_to_excel not callable: {e}")
        
    except Exception as e:
        issues.append(f"Error handling test failed: {e}")
    
    return issues

def test_dependency_handling():
    """Test dependency availability handling"""
    print("\n=== TESTING DEPENDENCY HANDLING ===")
    
    issues = []
    
    try:
        from email_processor import PANDAS_AVAILABLE, XLSXWRITER_AVAILABLE
        
        print(f"✓ PANDAS_AVAILABLE: {PANDAS_AVAILABLE}")
        print(f"✓ XLSXWRITER_AVAILABLE: {XLSXWRITER_AVAILABLE}")
        
        # If dependencies are available, test that optimized method would be used
        if PANDAS_AVAILABLE and XLSXWRITER_AVAILABLE:
            print("✓ Both dependencies available - optimized method should be used")
        else:
            print("⚠️ Dependencies missing - fallback method will be used")
            if not PANDAS_AVAILABLE:
                print("  - pandas not available")
            if not XLSXWRITER_AVAILABLE:
                print("  - xlsxwriter not available")
        
    except Exception as e:
        issues.append(f"Dependency handling test failed: {e}")
    
    return issues

def test_method_call_chain():
    """Test the method call chain for both classes"""
    print("\n=== TESTING METHOD CALL CHAIN ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # ProcessingThread call chain:
        # export_to_excel -> _export_to_excel_optimized -> _export_to_excel_openpyxl -> export_to_excel_original
        pt = ProcessingThread([], "test.xlsx")
        
        call_chain_pt = [
            'export_to_excel',
            '_export_to_excel_optimized', 
            '_export_to_excel_openpyxl',
            'export_to_excel_original'
        ]
        
        for method in call_chain_pt:
            if hasattr(pt, method):
                print(f"✓ ProcessingThread.{method} exists")
            else:
                issues.append(f"ProcessingThread missing {method}")
        
        # ThreadedProcessingThread call chain:
        # export_to_excel -> _export_to_excel_optimized -> _export_to_excel_openpyxl (direct implementation)
        tpt = ThreadedProcessingThread([], "test.xlsx")
        
        call_chain_tpt = [
            'export_to_excel',
            '_export_to_excel_optimized',
            '_export_to_excel_openpyxl'
        ]
        
        for method in call_chain_tpt:
            if hasattr(tpt, method):
                print(f"✓ ThreadedProcessingThread.{method} exists")
            else:
                issues.append(f"ThreadedProcessingThread missing {method}")
        
        # ThreadedProcessingThread should NOT have export_to_excel_original
        if hasattr(tpt, 'export_to_excel_original'):
            issues.append("ThreadedProcessingThread should not have export_to_excel_original (inconsistent with ProcessingThread)")
        else:
            print("✓ ThreadedProcessingThread correctly uses direct implementation")
        
    except Exception as e:
        issues.append(f"Method call chain test failed: {e}")
    
    return issues

def main():
    """Run detailed compatibility tests"""
    print("🔍 DETAILED COMPATIBILITY TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_sanitization_access,
        test_fallback_consistency,
        test_data_validation_methods,
        test_error_handling_paths,
        test_dependency_handling,
        test_method_call_chain,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ NO ISSUES FOUND - All detailed tests passed!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
