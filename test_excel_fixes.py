#!/usr/bin/env python3
"""
Test script to verify Excel corruption fixes work correctly
"""

import openpyxl
import warnings
import zipfile
import xml.etree.ElementTree as ET
from io import BytesIO
import os
import tempfile

def test_shared_strings_fix():
    """Test that the shared strings fix prevents inline string bloat"""
    print("=== TESTING SHARED STRINGS FIX ===")
    
    # Create test data with repeated strings (should use shared strings)
    test_data = [
        ["Header 1", "Header 2", "Header 3"],
        ["Repeated String", "Another String", "Repeated String"],
        ["Different String", "Repeated String", "Another String"],
        ["Repeated String", "Different String", "Repeated String"],
        ["Another String", "Repeated String", "Different String"],
    ]
    
    # Test 1: Original method (should create inline strings)
    print("\n1. Testing original method (inline strings):")
    wb1 = openpyxl.Workbook()
    ws1 = wb1.active
    
    for row_idx, row_data in enumerate(test_data, 1):
        for col_idx, value in enumerate(row_data, 1):
            ws1.cell(row=row_idx, column=col_idx, value=value)
    
    buffer1 = BytesIO()
    wb1.save(buffer1)
    analyze_excel_structure(buffer1, "Original Method")
    
    # Test 2: Fixed method (should use shared strings)
    print("\n2. Testing fixed method (shared strings):")
    wb2 = openpyxl.Workbook()
    ws2 = wb2.active
    
    # Apply the fix
    wb2.shared_strings = []  # Initialize shared strings
    
    for row_idx, row_data in enumerate(test_data, 1):
        for col_idx, value in enumerate(row_data, 1):
            if isinstance(value, str) and value:
                cell = ws2.cell(row=row_idx, column=col_idx)
                cell.value = value
                cell.data_type = 's'  # Force shared string reference
            else:
                ws2.cell(row=row_idx, column=col_idx, value=value)
    
    buffer2 = BytesIO()
    wb2.save(buffer2)
    analyze_excel_structure(buffer2, "Fixed Method")
    
    return True

def analyze_excel_structure(buffer, method_name):
    """Analyze Excel file structure to check for shared strings usage"""
    buffer.seek(0)
    
    try:
        with zipfile.ZipFile(buffer, 'r') as zip_file:
            files = zip_file.namelist()
            
            # Check for shared strings file
            has_shared_strings = 'xl/sharedStrings.xml' in files
            print(f"  {method_name}:")
            print(f"    Has shared strings file: {has_shared_strings}")
            
            if has_shared_strings:
                shared_content = zip_file.read('xl/sharedStrings.xml')
                print(f"    Shared strings size: {len(shared_content)} bytes")
                
                # Count shared string items
                try:
                    root = ET.fromstring(shared_content)
                    items = root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si')
                    print(f"    Shared string items: {len(items)}")
                except ET.ParseError:
                    print(f"    Shared strings XML parse error")
            
            # Check worksheet
            if 'xl/worksheets/sheet1.xml' in files:
                sheet_content = zip_file.read('xl/worksheets/sheet1.xml')
                print(f"    Worksheet size: {len(sheet_content)} bytes")
                
                # Count inline strings
                inline_count = sheet_content.count(b'<is>')
                print(f"    Inline string elements: {inline_count}")
                
                # Count cell references to shared strings
                shared_ref_count = sheet_content.count(b't="s"')
                print(f"    Shared string references: {shared_ref_count}")
    
    except Exception as e:
        print(f"    Error analyzing structure: {e}")

def test_large_dataset_performance():
    """Test performance with larger dataset similar to email processing"""
    print("\n=== TESTING LARGE DATASET PERFORMANCE ===")
    
    # Create larger test dataset
    large_data = []
    
    # Headers
    headers = [
        'Filename', 'Subject', 'From Email', 'From Name', 'Body Content',
        'Attachments', 'Date', 'Message ID', 'Content Type', 'Error'
    ]
    large_data.append(headers)
    
    # Generate test email data
    for i in range(100):  # 100 test emails
        row = [
            f'email_{i:03d}.eml',
            f'Test Subject {i % 10}',  # Repeated subjects
            f'sender{i % 5}@example.com',  # Repeated senders
            f'Sender Name {i % 5}',  # Repeated names
            f'This is email body content for email {i}. ' * 10,  # Long content
            f'attachment_{i}.pdf' if i % 3 == 0 else '',
            f'2024-01-{(i % 28) + 1:02d} 10:00:00',
            f'<msg{i}@example.com>',
            'text/plain',
            '' if i % 10 != 0 else f'Processing error {i}'
        ]
        large_data.append(row)
    
    print(f"Testing with {len(large_data)} rows, {len(headers)} columns")
    
    # Test with fixed method
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Email Processing Results"
    
    # Apply fixes
    wb.shared_strings = []
    
    import time
    start_time = time.time()
    
    for row_idx, row_data in enumerate(large_data, 1):
        for col_idx, value in enumerate(row_data, 1):
            if isinstance(value, str) and value:
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = value
                cell.data_type = 's'
            else:
                ws.cell(row=row_idx, column=col_idx, value=value)
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        wb.save(tmp_file.name)
        file_size = os.path.getsize(tmp_file.name)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"Processing time: {processing_time:.2f} seconds")
        print(f"File size: {file_size:,} bytes")
        
        # Analyze structure
        with open(tmp_file.name, 'rb') as f:
            buffer = BytesIO(f.read())
        analyze_excel_structure(buffer, "Large Dataset Test")
        
        # Verify file can be opened
        try:
            test_wb = openpyxl.load_workbook(tmp_file.name, read_only=True)
            print(f"✓ File verification: SUCCESS")
            print(f"  Rows: {test_wb.active.max_row}")
            print(f"  Columns: {test_wb.active.max_column}")
            test_wb.close()
        except Exception as e:
            print(f"❌ File verification: FAILED - {e}")
        
        # Clean up
        os.unlink(tmp_file.name)

def test_sanitization_integration():
    """Test that sanitization works with the shared strings fix"""
    print("\n=== TESTING SANITIZATION INTEGRATION ===")
    
    from email_processor import EmailParser
    parser = EmailParser()
    
    # Test problematic strings
    test_strings = [
        'Normal string',
        'String with \x0B control char',
        'String with & ampersand',
        'String with < bracket',
        'Very long string: ' + 'x' * 1000,
        'Unicode: 你好世界 🎉',
        'Mixed: Hello\x0BWorld\x1F测试',
    ]
    
    wb = openpyxl.Workbook()
    ws = wb.active
    wb.shared_strings = []
    
    for i, test_string in enumerate(test_strings, 1):
        # Apply sanitization
        sanitized = parser.sanitize_excel_value(test_string)
        
        # Apply shared strings fix
        cell = ws.cell(row=i, column=1)
        cell.value = sanitized
        cell.data_type = 's'
        
        print(f"Row {i}: {len(sanitized)} chars, sanitized: {test_string != sanitized}")
    
    # Test save
    buffer = BytesIO()
    wb.save(buffer)
    analyze_excel_structure(buffer, "Sanitization + Shared Strings")
    
    print("✓ Sanitization integration test completed")

if __name__ == '__main__':
    print("Testing Excel corruption fixes...")
    
    try:
        test_shared_strings_fix()
        test_large_dataset_performance()
        test_sanitization_integration()
        
        print("\n" + "="*60)
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY")
        print("The Excel corruption fixes are working correctly!")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
