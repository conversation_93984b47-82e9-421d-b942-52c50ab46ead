#!/usr/bin/env python3
"""
Research openpyxl shared strings behavior
"""

import openpyxl
from openpyxl.workbook.workbook import Workbook
import zipfile
from io import BytesIO

def research_shared_strings():
    """Research how to properly enable shared strings in openpyxl"""
    print("=== RESEARCHING OPENPYXL SHARED STRINGS ===")
    
    # Test different approaches
    approaches = [
        ("Default behavior", create_default_workbook),
        ("Force data_type='s'", create_forced_shared_strings),
        ("Use write_only mode", create_write_only_workbook),
        ("Manual shared strings", create_manual_shared_strings),
    ]
    
    for name, func in approaches:
        print(f"\n{name}:")
        try:
            buffer = func()
            analyze_structure(buffer)
        except Exception as e:
            print(f"  Error: {e}")

def create_default_workbook():
    """Create workbook with default openpyxl behavior"""
    wb = openpyxl.Workbook()
    ws = wb.active
    
    # Add repeated strings
    strings = ["Repeated String", "Another String", "Repeated String", "Different String", "Repeated String"]
    for i, s in enumerate(strings, 1):
        ws.cell(row=i, column=1, value=s)
    
    buffer = BytesIO()
    wb.save(buffer)
    return buffer

def create_forced_shared_strings():
    """Try to force shared strings by setting data_type"""
    wb = openpyxl.Workbook()
    ws = wb.active
    
    strings = ["Repeated String", "Another String", "Repeated String", "Different String", "Repeated String"]
    for i, s in enumerate(strings, 1):
        cell = ws.cell(row=i, column=1)
        cell.value = s
        cell.data_type = 's'  # Force shared string type
    
    buffer = BytesIO()
    wb.save(buffer)
    return buffer

def create_write_only_workbook():
    """Try write-only mode which might use shared strings"""
    wb = openpyxl.Workbook(write_only=True)
    ws = wb.create_sheet()
    
    strings = ["Repeated String", "Another String", "Repeated String", "Different String", "Repeated String"]
    for s in strings:
        ws.append([s])
    
    buffer = BytesIO()
    wb.save(buffer)
    return buffer

def create_manual_shared_strings():
    """Try to manually create shared strings table"""
    wb = openpyxl.Workbook()
    ws = wb.active
    
    # Try to access internal shared strings
    if hasattr(wb, '_shared_strings'):
        print("  Found _shared_strings attribute")
    
    # Create a shared strings table manually
    from openpyxl.workbook.workbook import Workbook
    
    strings = ["Repeated String", "Another String", "Repeated String", "Different String", "Repeated String"]
    
    # Try different approaches to enable shared strings
    unique_strings = list(set(strings))
    string_to_index = {s: i for i, s in enumerate(unique_strings)}
    
    for i, s in enumerate(strings, 1):
        cell = ws.cell(row=i, column=1)
        # Try to reference shared string by index
        string_index = string_to_index[s]
        cell.value = string_index
        cell.data_type = 's'
    
    buffer = BytesIO()
    wb.save(buffer)
    return buffer

def analyze_structure(buffer):
    """Analyze Excel file structure"""
    buffer.seek(0)
    
    try:
        with zipfile.ZipFile(buffer, 'r') as zip_file:
            files = zip_file.namelist()
            
            has_shared_strings = 'xl/sharedStrings.xml' in files
            print(f"  Has shared strings: {has_shared_strings}")
            
            if has_shared_strings:
                shared_content = zip_file.read('xl/sharedStrings.xml')
                print(f"  Shared strings size: {len(shared_content)} bytes")
            
            if 'xl/worksheets/sheet1.xml' in files:
                sheet_content = zip_file.read('xl/worksheets/sheet1.xml')
                inline_count = sheet_content.count(b'<is>')
                shared_ref_count = sheet_content.count(b't="s"')
                print(f"  Inline strings: {inline_count}")
                print(f"  Shared string refs: {shared_ref_count}")
                print(f"  Worksheet size: {len(sheet_content)} bytes")
    
    except Exception as e:
        print(f"  Analysis error: {e}")

def research_openpyxl_source():
    """Research openpyxl source code behavior"""
    print("\n=== RESEARCHING OPENPYXL SOURCE ===")
    
    # Check openpyxl version
    print(f"openpyxl version: {openpyxl.__version__}")
    
    # Look at workbook structure
    wb = openpyxl.Workbook()
    print(f"Workbook type: {type(wb)}")
    
    # Check for shared strings related attributes
    attrs = [attr for attr in dir(wb) if 'string' in attr.lower()]
    print(f"String-related attributes: {attrs}")
    
    # Check cell behavior
    ws = wb.active
    cell = ws.cell(row=1, column=1, value="test")
    print(f"Cell data_type: {cell.data_type}")
    print(f"Cell data_type options: s, inlineStr, str, n, b, e, d")
    
    # Check if openpyxl has optimization settings
    optimization_attrs = [attr for attr in dir(wb) if 'optim' in attr.lower() or 'write' in attr.lower()]
    print(f"Optimization attributes: {optimization_attrs}")

if __name__ == '__main__':
    research_shared_strings()
    research_openpyxl_source()
