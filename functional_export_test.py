#!/usr/bin/env python3
"""
Functional test of actual Excel export with both methods
"""

import sys
import os
import tempfile
import traceback
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def create_test_data():
    """Create realistic test data with problematic characters"""
    return [
        {
            'filename': 'test1.eml',
            'file_path': '/path/to/test1.eml',
            'subject': 'Test Subject with special chars: \x0B\x0C\x1C',
            'from_email': '<EMAIL>',
            'from_name': 'Sender Name',
            'from_full': 'Sender Name <<EMAIL>>',
            'to_emails': '<EMAIL>',
            'to_names': 'Recipient Name',
            'to_full': 'Recipient Name <<EMAIL>>',
            'cc_emails': '',
            'cc_names': '',
            'cc_full': '',
            'bcc_emails': '',
            'bcc_names': '',
            'bcc_full': '',
            'date': '2024-01-01 10:00:00',
            'message_id': '<<EMAIL>>',
            'reply_to': '',
            'priority': 'Normal',
            'importance': 'Normal',
            'content_type': 'text/plain',
            'body': 'Test body with problematic chars: \x0B\x0C\x1C and long content ' * 100,
            'attachments_count': 1,
            'attachments_names': 'document.pdf',
            'attachments_sizes': '1024 bytes',
            'total_attachments_size': 1024,
            'archive_file_list': '',
            'archive_file_count': 0,
            'archive_total_size': 0,
            'ocr_extracted_text': 'OCR text with special chars: \x0B\x0C',
            'document_content': 'Document content',
            'attachment_processing_status': 'success',
            'attachment_processing_time': 0.5,
            'embedded_image_ocr_text': '',
            'embedded_images_count': 0,
            'embedded_images_info': '',
            'encoding_detected': 'utf-8',
            'has_html': False,
            'error': ''
        },
        {
            'filename': 'test2.eml',
            'file_path': '/path/to/test2.eml',
            'subject': 'Another Test Subject',
            'from_email': '<EMAIL>',
            'from_name': 'Another Sender',
            'from_full': 'Another Sender <<EMAIL>>',
            'to_emails': '<EMAIL>',
            'to_names': 'Another Recipient',
            'to_full': 'Another Recipient <<EMAIL>>',
            'cc_emails': '<EMAIL>',
            'cc_names': 'CC Person',
            'cc_full': 'CC Person <<EMAIL>>',
            'bcc_emails': '',
            'bcc_names': '',
            'bcc_full': '',
            'date': '2024-01-02 11:00:00',
            'message_id': '<<EMAIL>>',
            'reply_to': '<EMAIL>',
            'priority': 'High',
            'importance': 'High',
            'content_type': 'text/html',
            'body': 'HTML body content with <b>formatting</b>',
            'attachments_count': 2,
            'attachments_names': 'image.jpg, document.docx',
            'attachments_sizes': '2048 bytes, 4096 bytes',
            'total_attachments_size': 6144,
            'archive_file_list': 'file1.txt, file2.txt',
            'archive_file_count': 2,
            'archive_total_size': 1024,
            'ocr_extracted_text': 'Extracted text from image',
            'document_content': 'Document text content',
            'attachment_processing_status': 'partial',
            'attachment_processing_time': 1.2,
            'embedded_image_ocr_text': 'Embedded image text',
            'embedded_images_count': 1,
            'embedded_images_info': 'image1.png (100x100)',
            'encoding_detected': 'utf-8',
            'has_html': True,
            'error': 'Warning: Some attachments could not be processed'
        }
    ]

def analyze_excel_structure(file_path):
    """Analyze Excel file structure"""
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            files = zip_file.namelist()
            
            # Check for shared strings
            has_shared_strings = 'xl/sharedStrings.xml' in files
            
            result = {
                'has_shared_strings': has_shared_strings,
                'shared_strings_size': 0,
                'shared_string_count': 0,
                'worksheet_size': 0,
                'inline_string_count': 0,
                'shared_ref_count': 0
            }
            
            if has_shared_strings:
                shared_content = zip_file.read('xl/sharedStrings.xml')
                result['shared_strings_size'] = len(shared_content)
                
                try:
                    root = ET.fromstring(shared_content)
                    items = root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si')
                    result['shared_string_count'] = len(items)
                except ET.ParseError:
                    pass
            
            # Check worksheet
            if 'xl/worksheets/sheet1.xml' in files:
                sheet_content = zip_file.read('xl/worksheets/sheet1.xml')
                result['worksheet_size'] = len(sheet_content)
                result['inline_string_count'] = sheet_content.count(b'<is>')
                result['shared_ref_count'] = sheet_content.count(b't="s"')
            
            return result
    
    except Exception as e:
        return {'error': str(e)}

def test_optimized_export():
    """Test optimized pandas+xlsxwriter export"""
    print("=== TESTING OPTIMIZED EXPORT ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create test data
        test_data = create_test_data()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Create ProcessingThread instance
            pt = ProcessingThread([], output_path)
            pt.results = test_data
            
            # Test optimized export directly
            result = pt._export_to_excel_optimized()
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✓ Optimized export created file: {file_size:,} bytes")
                
                # Analyze structure
                structure = analyze_excel_structure(output_path)
                if 'error' in structure:
                    issues.append(f"Structure analysis failed: {structure['error']}")
                else:
                    print(f"  - Has shared strings: {structure['has_shared_strings']}")
                    print(f"  - Shared string count: {structure['shared_string_count']:,}")
                    print(f"  - Inline string count: {structure['inline_string_count']:,}")
                    print(f"  - Worksheet size: {structure['worksheet_size']:,} bytes")
                    
                    if structure['has_shared_strings'] and structure['inline_string_count'] == 0:
                        print("✓ Optimized export uses shared strings correctly")
                    else:
                        issues.append("Optimized export not using shared strings properly")
                
                # Test file can be read
                try:
                    import pandas as pd
                    df = pd.read_excel(output_path)
                    print(f"✓ File readable with pandas: {df.shape}")
                    
                    # Check for data integrity
                    if len(df) == len(test_data):
                        print("✓ All records exported")
                    else:
                        issues.append(f"Record count mismatch: expected {len(test_data)}, got {len(df)}")
                    
                except Exception as e:
                    issues.append(f"File not readable with pandas: {e}")
                
            else:
                issues.append("Optimized export did not create file")
        
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Optimized export test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_fallback_export():
    """Test fallback openpyxl export"""
    print("\n=== TESTING FALLBACK EXPORT ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create test data
        test_data = create_test_data()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Create ProcessingThread instance
            pt = ProcessingThread([], output_path)
            pt.results = test_data
            
            # Test fallback export directly
            result = pt._export_to_excel_openpyxl()
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✓ Fallback export created file: {file_size:,} bytes")
                
                # Analyze structure
                structure = analyze_excel_structure(output_path)
                if 'error' in structure:
                    issues.append(f"Structure analysis failed: {structure['error']}")
                else:
                    print(f"  - Has shared strings: {structure['has_shared_strings']}")
                    print(f"  - Shared string count: {structure['shared_string_count']:,}")
                    print(f"  - Inline string count: {structure['inline_string_count']:,}")
                    print(f"  - Worksheet size: {structure['worksheet_size']:,} bytes")
                
                # Test file can be read
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(output_path, read_only=True)
                    ws = wb.active
                    row_count = ws.max_row
                    col_count = ws.max_column
                    wb.close()
                    print(f"✓ File readable with openpyxl: {row_count} rows, {col_count} columns")
                    
                    # Check for data integrity (including header row)
                    if row_count - 1 == len(test_data):
                        print("✓ All records exported")
                    else:
                        issues.append(f"Record count mismatch: expected {len(test_data)}, got {row_count - 1}")
                    
                except Exception as e:
                    issues.append(f"File not readable with openpyxl: {e}")
                
            else:
                issues.append("Fallback export did not create file")
        
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Fallback export test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_threaded_export():
    """Test ThreadedProcessingThread export"""
    print("\n=== TESTING THREADED EXPORT ===")
    
    issues = []
    
    try:
        from email_processor import ThreadedProcessingThread
        
        # Create test data
        test_data = create_test_data()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Create ThreadedProcessingThread instance
            tpt = ThreadedProcessingThread([], output_path)
            tpt.results = test_data
            
            # Test optimized export
            result = tpt._export_to_excel_optimized()
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✓ Threaded optimized export created file: {file_size:,} bytes")
                
                # Test file can be read
                try:
                    import pandas as pd
                    df = pd.read_excel(output_path)
                    print(f"✓ Threaded file readable: {df.shape}")
                    
                    if len(df) == len(test_data):
                        print("✓ All records exported by threaded method")
                    else:
                        issues.append(f"Threaded record count mismatch: expected {len(test_data)}, got {len(df)}")
                    
                except Exception as e:
                    issues.append(f"Threaded file not readable: {e}")
                
            else:
                issues.append("Threaded export did not create file")
        
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Threaded export test failed: {e}")
        traceback.print_exc()
    
    return issues

def main():
    """Run functional export tests"""
    print("🧪 FUNCTIONAL EXPORT TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_optimized_export,
        test_fallback_export,
        test_threaded_export,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ ALL FUNCTIONAL TESTS PASSED!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
