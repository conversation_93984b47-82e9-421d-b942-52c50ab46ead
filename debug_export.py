#!/usr/bin/env python3
"""
Debug the export issue
"""

import sys
import os
import tempfile
import traceback

# Add current directory to path
sys.path.insert(0, '.')

def debug_optimized_export():
    """Debug the optimized export method"""
    print("=== DEBUGGING OPTIMIZED EXPORT ===")
    
    try:
        from email_processor import ProcessingThread
        
        # Simple test data
        test_data = [
            {
                'filename': 'test.eml',
                'subject': 'Test Subject',
                'body': 'Test body'
            }
        ]
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        print(f"Output path: {output_path}")
        
        try:
            # Create ProcessingThread instance
            pt = ProcessingThread([], output_path)
            pt.results = test_data
            
            print("Calling _export_to_excel_optimized...")
            result = pt._export_to_excel_optimized()
            print(f"Result: {result}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"File created: {file_size:,} bytes")
                
                if file_size > 0:
                    # Try to read the file content
                    with open(output_path, 'rb') as f:
                        first_bytes = f.read(100)
                        print(f"First 100 bytes: {first_bytes}")
                        
                    # Check if it's a valid Excel file
                    import zipfile
                    try:
                        with zipfile.ZipFile(output_path, 'r') as zf:
                            files = zf.namelist()
                            print(f"Excel file structure: {files[:10]}")  # First 10 files
                    except zipfile.BadZipFile:
                        print("❌ Not a valid ZIP/Excel file")
                        
                        # Check if it's text content
                        try:
                            with open(output_path, 'r', encoding='utf-8') as f:
                                content = f.read(500)
                                print(f"Text content: {content}")
                        except:
                            print("❌ Cannot read as text either")
                else:
                    print("❌ File is empty")
            else:
                print("❌ File was not created")
        
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        traceback.print_exc()

def debug_fallback_export():
    """Debug the fallback export method"""
    print("\n=== DEBUGGING FALLBACK EXPORT ===")
    
    try:
        from email_processor import ProcessingThread
        
        # Simple test data
        test_data = [
            {
                'filename': 'test.eml',
                'subject': 'Test Subject',
                'body': 'Test body'
            }
        ]
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        print(f"Output path: {output_path}")
        
        try:
            # Create ProcessingThread instance
            pt = ProcessingThread([], output_path)
            pt.results = test_data
            
            print("Calling _export_to_excel_openpyxl...")
            result = pt._export_to_excel_openpyxl()
            print(f"Result: {result}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"File created: {file_size:,} bytes")
                
                if file_size > 0:
                    # Check if it's a valid Excel file
                    import zipfile
                    try:
                        with zipfile.ZipFile(output_path, 'r') as zf:
                            files = zf.namelist()
                            print(f"Excel file structure: {files[:10]}")  # First 10 files
                    except zipfile.BadZipFile:
                        print("❌ Not a valid ZIP/Excel file")
                else:
                    print("❌ File is empty")
            else:
                print("❌ File was not created")
        
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        traceback.print_exc()

def test_pandas_xlsxwriter_directly():
    """Test pandas + xlsxwriter directly"""
    print("\n=== TESTING PANDAS + XLSXWRITER DIRECTLY ===")
    
    try:
        import pandas as pd
        
        # Simple test data
        data = {
            'Original Filename': ['test.eml'],
            'Subject': ['Test Subject'],
            'Body Content': ['Test body']
        }
        
        df = pd.DataFrame(data)
        print(f"DataFrame created: {df.shape}")
        print(df.head())
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Export using xlsxwriter
            with pd.ExcelWriter(output_path, engine='xlsxwriter', 
                              engine_kwargs={'options': {'strings_to_numbers': False, 'strings_to_urls': False}}) as writer:
                df.to_excel(writer, sheet_name='Test Sheet', index=False)
                
                # Get workbook and worksheet for formatting
                workbook = writer.book
                worksheet = writer.sheets['Test Sheet']
                
                # Apply formatting
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'bg_color': '#D7E4BC'
                })
                
                # Format headers
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
            
            print("Direct pandas+xlsxwriter export completed")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✓ File created: {file_size:,} bytes")
                
                # Verify it's a valid Excel file
                import zipfile
                with zipfile.ZipFile(output_path, 'r') as zf:
                    files = zf.namelist()
                    print(f"✓ Valid Excel file with {len(files)} internal files")
                
                # Test reading back
                df_read = pd.read_excel(output_path)
                print(f"✓ File readable: {df_read.shape}")
                print(df_read.head())
            else:
                print("❌ File was not created")
        
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        print(f"❌ Direct test failed: {e}")
        traceback.print_exc()

def main():
    """Run debug tests"""
    print("🐛 DEBUGGING EXPORT ISSUES")
    print("=" * 60)
    
    debug_optimized_export()
    debug_fallback_export()
    test_pandas_xlsxwriter_directly()

if __name__ == '__main__':
    main()
