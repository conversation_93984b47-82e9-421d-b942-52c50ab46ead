#!/usr/bin/env python3
"""
Comprehensive code review test for email_processor.py
Identifies compatibility issues between pandas+xlsxwriter and openpyxl implementations
"""

import sys
import os
import tempfile
import traceback
from pathlib import Path

# Add current directory to path to import email_processor
sys.path.insert(0, '.')

def test_import_compatibility():
    """Test that all imports work correctly"""
    print("=== TESTING IMPORT COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import EmailParser, ProcessingThread, ThreadedProcessingThread
        print("✓ Core classes imported successfully")
    except ImportError as e:
        issues.append(f"Core import failed: {e}")
        return issues
    
    # Check dependency flags
    try:
        from email_processor import PANDAS_AVAILABLE, XLSXWRITER_AVAILABLE
        print(f"✓ PANDAS_AVAILABLE: {PANDAS_AVAILABLE}")
        print(f"✓ XLSXWRITER_AVAILABLE: {XLSXWRITER_AVAILABLE}")
    except ImportError as e:
        issues.append(f"Dependency flags not available: {e}")
    
    return issues

def test_method_signatures():
    """Test that method signatures remain unchanged"""
    print("\n=== TESTING METHOD SIGNATURES ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Test ProcessingThread
        pt = ProcessingThread([], "test.xlsx")
        
        # Check that export_to_excel method exists and is callable
        if not hasattr(pt, 'export_to_excel'):
            issues.append("ProcessingThread missing export_to_excel method")
        elif not callable(getattr(pt, 'export_to_excel')):
            issues.append("ProcessingThread export_to_excel is not callable")
        else:
            print("✓ ProcessingThread.export_to_excel signature OK")
        
        # Test ThreadedProcessingThread
        tpt = ThreadedProcessingThread([], "test.xlsx")
        
        if not hasattr(tpt, 'export_to_excel'):
            issues.append("ThreadedProcessingThread missing export_to_excel method")
        elif not callable(getattr(tpt, 'export_to_excel')):
            issues.append("ThreadedProcessingThread export_to_excel is not callable")
        else:
            print("✓ ThreadedProcessingThread.export_to_excel signature OK")
            
        # Check for new methods
        expected_methods = ['_export_to_excel_optimized', '_export_to_excel_openpyxl', '_verify_excel_file_integrity']
        for method in expected_methods:
            if hasattr(pt, method):
                print(f"✓ ProcessingThread.{method} exists")
            else:
                issues.append(f"ProcessingThread missing {method}")
                
            if hasattr(tpt, method):
                print(f"✓ ThreadedProcessingThread.{method} exists")
            else:
                issues.append(f"ThreadedProcessingThread missing {method}")
        
    except Exception as e:
        issues.append(f"Method signature test failed: {e}")
    
    return issues

def test_column_mapping_consistency():
    """Test that column mappings are consistent between implementations"""
    print("\n=== TESTING COLUMN MAPPING CONSISTENCY ===")
    
    issues = []
    
    try:
        # Expected headers from original openpyxl implementation
        expected_headers = [
            'Original Filename', 'File Path', 'Subject', 'From Email', 'From Name', 'From Full',
            'To Emails', 'To Names', 'To Full', 'CC Emails', 'CC Names', 'CC Full',
            'BCC Emails', 'BCC Names', 'BCC Full', 'Date', 'Message ID', 'Reply To',
            'Priority', 'Importance', 'Content Type', 'Body Content', 'Attachments Count',
            'Attachment Names', 'Attachment Sizes', 'Total Attachments Size',
            'Archive File List', 'Archive File Count', 'Archive Total Size',
            'OCR Extracted Text', 'Document Content', 'Attachment Processing Status',
            'Attachment Processing Time',
            'Embedded Image OCR Text', 'Embedded Images Count', 'Embedded Images Info',
            'Encoding Detected', 'Has HTML', 'Processing Error'
        ]
        
        # Check if pandas column mapping produces the same headers
        column_mapping = {
            'filename': 'Original Filename',
            'file_path': 'File Path',
            'subject': 'Subject',
            'from_email': 'From Email',
            'from_name': 'From Name',
            'from_full': 'From Full',
            'to_emails': 'To Emails',
            'to_names': 'To Names',
            'to_full': 'To Full',
            'cc_emails': 'CC Emails',
            'cc_names': 'CC Names',
            'cc_full': 'CC Full',
            'bcc_emails': 'BCC Emails',
            'bcc_names': 'BCC Names',
            'bcc_full': 'BCC Full',
            'date': 'Date',
            'message_id': 'Message ID',
            'reply_to': 'Reply To',
            'priority': 'Priority',
            'importance': 'Importance',
            'content_type': 'Content Type',
            'body': 'Body Content',
            'attachments_count': 'Attachments Count',
            'attachments_names': 'Attachment Names',
            'attachments_sizes': 'Attachment Sizes',
            'total_attachments_size': 'Total Attachments Size',
            'archive_file_list': 'Archive File List',
            'archive_file_count': 'Archive File Count',
            'archive_total_size': 'Archive Total Size',
            'ocr_extracted_text': 'OCR Extracted Text',
            'document_content': 'Document Content',
            'attachment_processing_status': 'Attachment Processing Status',
            'attachment_processing_time': 'Attachment Processing Time',
            'embedded_image_ocr_text': 'Embedded Image OCR Text',
            'embedded_images_count': 'Embedded Images Count',
            'embedded_images_info': 'Embedded Images Info',
            'encoding_detected': 'Encoding Detected',
            'has_html': 'Has HTML',
            'error': 'Processing Error'
        }
        
        mapped_headers = list(column_mapping.values())
        
        # Compare headers
        if len(expected_headers) != len(mapped_headers):
            issues.append(f"Header count mismatch: expected {len(expected_headers)}, got {len(mapped_headers)}")
        
        for i, (expected, mapped) in enumerate(zip(expected_headers, mapped_headers)):
            if expected != mapped:
                issues.append(f"Header mismatch at position {i}: expected '{expected}', got '{mapped}'")
        
        if not issues:
            print("✓ Column mappings are consistent")
        
    except Exception as e:
        issues.append(f"Column mapping test failed: {e}")
    
    return issues

def test_fallback_mechanism():
    """Test fallback mechanism works correctly"""
    print("\n=== TESTING FALLBACK MECHANISM ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create test instance
        pt = ProcessingThread([], "test.xlsx")
        
        # Test that fallback method exists
        if not hasattr(pt, '_export_to_excel_openpyxl'):
            issues.append("Missing _export_to_excel_openpyxl fallback method")
        
        # Test that original method exists
        if not hasattr(pt, 'export_to_excel_original'):
            issues.append("Missing export_to_excel_original method")
        
        print("✓ Fallback methods exist")
        
    except Exception as e:
        issues.append(f"Fallback mechanism test failed: {e}")
    
    return issues

def test_data_structure_compatibility():
    """Test that data structures are compatible"""
    print("\n=== TESTING DATA STRUCTURE COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import EmailParser
        
        parser = EmailParser()
        
        # Test sample data structure
        sample_result = {
            'filename': 'test.eml',
            'file_path': '/path/test.eml',
            'subject': 'Test Subject',
            'from_email': '<EMAIL>',
            'from_name': 'Test User',
            'from_full': 'Test User <<EMAIL>>',
            'to_emails': '<EMAIL>',
            'to_names': 'Recipient',
            'to_full': 'Recipient <<EMAIL>>',
            'cc_emails': '',
            'cc_names': '',
            'cc_full': '',
            'bcc_emails': '',
            'bcc_names': '',
            'bcc_full': '',
            'date': '2024-01-01 10:00:00',
            'message_id': '<<EMAIL>>',
            'reply_to': '',
            'priority': 'Normal',
            'importance': 'Normal',
            'content_type': 'text/plain',
            'body': 'Test body content',
            'attachments_count': 0,
            'attachments_names': '',
            'attachments_sizes': '',
            'total_attachments_size': 0,
            'archive_file_list': '',
            'archive_file_count': 0,
            'archive_total_size': 0,
            'ocr_extracted_text': '',
            'document_content': '',
            'attachment_processing_status': '',
            'attachment_processing_time': 0,
            'embedded_image_ocr_text': '',
            'embedded_images_count': 0,
            'embedded_images_info': '',
            'encoding_detected': 'utf-8',
            'has_html': False,
            'error': ''
        }
        
        # Test sanitization on all string fields
        for key, value in sample_result.items():
            if isinstance(value, str):
                try:
                    sanitized = parser.sanitize_excel_value(value)
                    if sanitized is None:
                        issues.append(f"Sanitization returned None for field '{key}'")
                except Exception as e:
                    issues.append(f"Sanitization failed for field '{key}': {e}")
        
        print("✓ Data structure compatibility OK")
        
    except Exception as e:
        issues.append(f"Data structure test failed: {e}")
    
    return issues

def main():
    """Run comprehensive review tests"""
    print("🔍 COMPREHENSIVE CODE REVIEW TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_import_compatibility,
        test_method_signatures,
        test_column_mapping_consistency,
        test_fallback_mechanism,
        test_data_structure_compatibility,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ NO ISSUES FOUND - All tests passed!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
