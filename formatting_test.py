#!/usr/bin/env python3
"""
Test Excel formatting features in both export methods
"""

import sys
import os
import tempfile
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_header_formatting():
    """Test that headers are formatted correctly in both methods"""
    print("=== TESTING HEADER FORMATTING ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        test_data = [{'filename': 'test.eml', 'subject': 'Test Subject', 'body': 'Test body'}]
        
        # Test optimized method
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path_opt = tmp_file.name
        
        try:
            pt = ProcessingThread([], output_path_opt)
            pt.results = test_data
            
            result = pt._export_to_excel_optimized()
            
            if os.path.exists(output_path_opt):
                # Check if file can be opened and has proper structure
                import openpyxl
                wb = openpyxl.load_workbook(output_path_opt)
                ws = wb.active
                
                # Check first row (headers)
                first_row = [cell.value for cell in ws[1]]
                expected_headers = ['Original Filename', 'File Path', 'Subject']  # First few headers
                
                headers_match = all(h in first_row for h in expected_headers)
                if headers_match:
                    print("✓ Optimized method has correct headers")
                else:
                    issues.append("Optimized method headers don't match expected")
                
                wb.close()
            else:
                issues.append("Optimized method didn't create file")
        
        finally:
            if os.path.exists(output_path_opt):
                os.unlink(output_path_opt)
        
        # Test fallback method
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path_fb = tmp_file.name
        
        try:
            pt = ProcessingThread([], output_path_fb)
            pt.results = test_data
            
            result = pt._export_to_excel_openpyxl()
            
            if os.path.exists(output_path_fb):
                # Check if file can be opened and has proper structure
                import openpyxl
                wb = openpyxl.load_workbook(output_path_fb)
                ws = wb.active
                
                # Check first row (headers)
                first_row = [cell.value for cell in ws[1]]
                expected_headers = ['Original Filename', 'File Path', 'Subject']
                
                headers_match = all(h in first_row for h in expected_headers)
                if headers_match:
                    print("✓ Fallback method has correct headers")
                else:
                    issues.append("Fallback method headers don't match expected")
                
                # Check if headers are bold (openpyxl specific formatting)
                first_cell = ws.cell(row=1, column=1)
                if hasattr(first_cell, 'font') and first_cell.font.bold:
                    print("✓ Fallback method headers are bold")
                else:
                    issues.append("Fallback method headers are not bold")
                
                wb.close()
            else:
                issues.append("Fallback method didn't create file")
        
        finally:
            if os.path.exists(output_path_fb):
                os.unlink(output_path_fb)
    
    except Exception as e:
        issues.append(f"Header formatting test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_column_widths():
    """Test that column widths are set appropriately"""
    print("\n=== TESTING COLUMN WIDTHS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create test data with varying content lengths
        test_data = [
            {
                'filename': 'short.eml',
                'subject': 'Short',
                'body': 'Short body',
                'attachments_names': 'file.pdf',
                'ocr_extracted_text': 'Short OCR text'
            },
            {
                'filename': 'very_long_filename_that_should_affect_column_width.eml',
                'subject': 'Very long subject line that contains a lot of text and should affect column width calculations',
                'body': 'Very long body content that goes on and on and should be wrapped in the Excel cell ' * 10,
                'attachments_names': 'very_long_attachment_name_file.pdf, another_very_long_attachment_name.docx',
                'ocr_extracted_text': 'Very long OCR extracted text content that should be wrapped ' * 20
            }
        ]
        
        # Test optimized method
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            pt = ProcessingThread([], output_path)
            pt.results = test_data
            
            result = pt._export_to_excel_optimized()
            
            if os.path.exists(output_path):
                print("✓ Optimized method created file with varying content lengths")
                
                # Check file size is reasonable (not too large due to formatting issues)
                file_size = os.path.getsize(output_path)
                if file_size < 1_000_000:  # Less than 1MB for 2 records
                    print(f"✓ File size reasonable: {file_size:,} bytes")
                else:
                    issues.append(f"File size too large: {file_size:,} bytes")
                
                # Verify file can be read
                import pandas as pd
                df = pd.read_excel(output_path)
                if len(df) == len(test_data):
                    print("✓ All records with varying lengths exported correctly")
                else:
                    issues.append("Record count mismatch with varying content lengths")
            else:
                issues.append("Optimized method failed with varying content lengths")
        
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Column widths test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_data_type_handling():
    """Test that different data types are handled correctly"""
    print("\n=== TESTING DATA TYPE HANDLING ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create test data with different data types
        test_data = [
            {
                'filename': 'test.eml',
                'subject': 'String value',
                'attachments_count': 5,  # Integer
                'total_attachments_size': 1024,  # Integer
                'attachment_processing_time': 1.5,  # Float
                'has_html': True,  # Boolean
                'date': '2024-01-01 10:00:00',  # String (date-like)
                'body': None,  # None value
                'error': '',  # Empty string
            }
        ]
        
        # Test both methods
        methods = [
            ('optimized', '_export_to_excel_optimized'),
            ('fallback', '_export_to_excel_openpyxl')
        ]
        
        for method_name, method_attr in methods:
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                output_path = tmp_file.name
            
            try:
                pt = ProcessingThread([], output_path)
                pt.results = test_data
                
                method = getattr(pt, method_attr)
                result = method()
                
                if os.path.exists(output_path):
                    print(f"✓ {method_name.capitalize()} method handled mixed data types")
                    
                    # Verify file can be read and data types are preserved/converted appropriately
                    if method_name == 'optimized':
                        import pandas as pd
                        df = pd.read_excel(output_path)
                        
                        # Check that numeric values are preserved
                        if 'Attachments Count' in df.columns:
                            attachments_count = df['Attachments Count'].iloc[0]
                            if pd.notna(attachments_count) and attachments_count == 5:
                                print("✓ Integer values preserved in optimized method")
                            else:
                                issues.append(f"Integer value not preserved: {attachments_count}")
                        
                        # Check boolean handling
                        if 'Has HTML' in df.columns:
                            has_html = df['Has HTML'].iloc[0]
                            if pd.notna(has_html):
                                print("✓ Boolean values handled in optimized method")
                            else:
                                issues.append("Boolean value not handled properly")
                    
                    else:  # fallback method
                        import openpyxl
                        wb = openpyxl.load_workbook(output_path)
                        ws = wb.active
                        
                        # Check that data is present (basic validation)
                        if ws.max_row >= 2:  # Header + data row
                            print("✓ Fallback method preserved data structure")
                        else:
                            issues.append("Fallback method didn't preserve data structure")
                        
                        wb.close()
                else:
                    issues.append(f"{method_name.capitalize()} method failed with mixed data types")
            
            finally:
                if os.path.exists(output_path):
                    os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Data type handling test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_special_characters():
    """Test handling of special characters in formatting"""
    print("\n=== TESTING SPECIAL CHARACTERS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Test data with special characters that might affect formatting
        test_data = [
            {
                'filename': 'test_with_unicode_🎉.eml',
                'subject': 'Subject with émojis 🚀 and ñoñó characters',
                'body': 'Body with special chars: \n\t\r and unicode: 中文 العربية',
                'from_name': 'Sender with "quotes" and <brackets>',
                'attachments_names': 'file with spaces & symbols.pdf',
            }
        ]
        
        # Test both methods
        methods = [
            ('optimized', '_export_to_excel_optimized'),
            ('fallback', '_export_to_excel_openpyxl')
        ]
        
        for method_name, method_attr in methods:
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                output_path = tmp_file.name
            
            try:
                pt = ProcessingThread([], output_path)
                pt.results = test_data
                
                method = getattr(pt, method_attr)
                result = method()
                
                if os.path.exists(output_path):
                    print(f"✓ {method_name.capitalize()} method handled special characters")
                    
                    # Verify file integrity
                    file_size = os.path.getsize(output_path)
                    if file_size > 0:
                        print(f"✓ {method_name.capitalize()} method created valid file: {file_size:,} bytes")
                    else:
                        issues.append(f"{method_name.capitalize()} method created empty file")
                else:
                    issues.append(f"{method_name.capitalize()} method failed with special characters")
            
            finally:
                if os.path.exists(output_path):
                    os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Special characters test failed: {e}")
        traceback.print_exc()
    
    return issues

def main():
    """Run formatting tests"""
    print("🎨 FORMATTING FEATURES TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_header_formatting,
        test_column_widths,
        test_data_type_handling,
        test_special_characters,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ ALL FORMATTING TESTS PASSED!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
