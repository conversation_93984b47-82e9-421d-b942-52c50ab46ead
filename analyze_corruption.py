#!/usr/bin/env python3
"""
Comprehensive analysis script to identify Excel corruption root cause
"""

import zipfile
import xml.etree.ElementTree as ET
import openpyxl
import warnings
import re
from pathlib import Path

def analyze_excel_xml_structure(excel_path):
    """Analyze the internal XML structure of the Excel file"""
    print("=== ANALYZING EXCEL XML STRUCTURE ===")
    
    try:
        with zipfile.ZipFile(excel_path, 'r') as zip_file:
            # List all files in the Excel archive
            print(f"Files in Excel archive: {len(zip_file.namelist())}")
            
            # Focus on the problematic sheet1.xml
            if 'xl/worksheets/sheet1.xml' in zip_file.namelist():
                sheet_content = zip_file.read('xl/worksheets/sheet1.xml')
                print(f"Sheet1.xml size: {len(sheet_content):,} bytes")
                
                # Parse XML and look for string-related issues
                try:
                    root = ET.fromstring(sheet_content)
                    print("✓ XML parsing successful")
                    
                    # Analyze string references and inline strings
                    string_issues = analyze_string_elements(root)
                    if string_issues:
                        print("⚠️ String-related issues found:")
                        for issue in string_issues[:10]:  # Show first 10
                            print(f"  {issue}")
                    else:
                        print("✓ No obvious string element issues")
                        
                except ET.ParseError as e:
                    print(f"❌ XML parsing failed: {e}")
                    return False
                    
            # Check shared strings
            if 'xl/sharedStrings.xml' in zip_file.namelist():
                shared_strings = zip_file.read('xl/sharedStrings.xml')
                print(f"SharedStrings.xml size: {len(shared_strings):,} bytes")
                
                try:
                    shared_root = ET.fromstring(shared_strings)
                    shared_issues = analyze_shared_strings(shared_root)
                    if shared_issues:
                        print("⚠️ Shared strings issues found:")
                        for issue in shared_issues[:10]:
                            print(f"  {issue}")
                    else:
                        print("✓ No shared strings issues")
                except ET.ParseError as e:
                    print(f"❌ SharedStrings XML parsing failed: {e}")
                    
    except Exception as e:
        print(f"❌ Error analyzing Excel structure: {e}")
        return False
    
    return True

def analyze_string_elements(root):
    """Analyze string elements in worksheet XML for corruption"""
    issues = []
    
    # Look for inline strings (inlineStr elements)
    inline_strings = root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}is')
    print(f"Found {len(inline_strings)} inline string elements")
    
    for i, inline_str in enumerate(inline_strings[:100]):  # Check first 100
        text_elem = inline_str.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
        if text_elem is not None and text_elem.text:
            # Check for problematic characters in text content
            problematic_chars = find_problematic_chars(text_elem.text)
            if problematic_chars:
                issues.append(f"Inline string {i}: {problematic_chars}")
    
    # Look for cell values with string references
    cells = root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c')
    print(f"Found {len(cells)} cell elements")
    
    string_ref_issues = 0
    for cell in cells[:1000]:  # Check first 1000 cells
        cell_type = cell.get('t')
        if cell_type == 'inlineStr':
            # This cell has inline string - already checked above
            continue
        elif cell_type == 's' or cell_type == 'str':
            # String reference or formula string
            value_elem = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
            if value_elem is not None and value_elem.text:
                try:
                    # For shared string references, this should be a number
                    if cell_type == 's':
                        int(value_elem.text)  # Should be valid integer
                except ValueError:
                    string_ref_issues += 1
                    if string_ref_issues <= 5:
                        issues.append(f"Invalid string reference: {value_elem.text}")
    
    if string_ref_issues > 5:
        issues.append(f"... and {string_ref_issues - 5} more string reference issues")
    
    return issues

def analyze_shared_strings(root):
    """Analyze shared strings XML for corruption"""
    issues = []
    
    # Find all string items
    string_items = root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si')
    print(f"Found {len(string_items)} shared string items")
    
    for i, string_item in enumerate(string_items[:1000]):  # Check first 1000
        text_elem = string_item.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
        if text_elem is not None and text_elem.text:
            problematic_chars = find_problematic_chars(text_elem.text)
            if problematic_chars:
                issues.append(f"Shared string {i}: {problematic_chars}")
                if len(issues) >= 10:
                    break
    
    return issues

def find_problematic_chars(text):
    """Find characters that could cause XML corruption"""
    if not text:
        return []
    
    issues = []
    
    # Check for XML-invalid characters
    invalid_chars = []
    for i, char in enumerate(text):
        code = ord(char)
        if not (code == 0x09 or code == 0x0A or code == 0x0D or 
               (0x20 <= code <= 0xD7FF) or (0xE000 <= code <= 0xFFFD) or 
               (0x10000 <= code <= 0x10FFFF)):
            invalid_chars.append(f'pos{i}:0x{code:02X}')
            if len(invalid_chars) >= 3:
                break
    
    if invalid_chars:
        issues.append(f"Invalid XML chars: {invalid_chars}")
    
    # Check for unescaped XML entities
    unescaped = []
    for char in ['<', '>', '&']:
        if char in text:
            # Check if it's properly escaped
            if char == '&' and not re.search(r'&(amp|lt|gt|quot|apos);', text):
                unescaped.append(char)
            elif char in ['<', '>']:
                unescaped.append(char)
    
    if unescaped:
        issues.append(f"Unescaped XML: {unescaped}")
    
    return issues

def analyze_openpyxl_data_handling():
    """Test how openpyxl handles various data types"""
    print("\n=== ANALYZING OPENPYXL DATA HANDLING ===")
    
    # Test problematic data scenarios
    test_data = [
        "Normal string",
        "String with \x0B control char",
        "String with & unescaped",
        "String with < bracket",
        "String with > bracket", 
        "Very long string: " + "x" * 50000,
        "Unicode: 你好世界 🎉",
        "Mixed: Hello\x0BWorld\x1F测试",
        None,
        123,
        123.456,
        True,
        False,
    ]
    
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        
        for i, data in enumerate(test_data, 1):
            try:
                ws.cell(row=i, column=1, value=data)
                print(f"✓ Row {i}: {type(data).__name__} - {repr(str(data)[:50])}")
            except Exception as e:
                print(f"❌ Row {i}: {type(data).__name__} - Error: {e}")
        
        # Try to save to memory to test serialization
        from io import BytesIO
        buffer = BytesIO()
        wb.save(buffer)
        print(f"✓ Test workbook saved successfully ({len(buffer.getvalue())} bytes)")
        
    except Exception as e:
        print(f"❌ Error in openpyxl data handling test: {e}")

if __name__ == '__main__':
    excel_file = "email_analysis_20250919_113357.xlsx"
    
    if Path(excel_file).exists():
        print(f"Analyzing Excel file: {excel_file}")
        analyze_excel_xml_structure(excel_file)
        analyze_openpyxl_data_handling()
    else:
        print(f"Excel file not found: {excel_file}")
        analyze_openpyxl_data_handling()
