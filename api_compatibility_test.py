#!/usr/bin/env python3
"""
Test API compatibility - ensure all public method signatures remain unchanged
"""

import sys
import inspect
import traceback

# Add current directory to path
sys.path.insert(0, '.')

def test_processing_thread_api():
    """Test ProcessingThread public API compatibility"""
    print("=== TESTING PROCESSINGTHREAD API COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Test constructor signature
        try:
            pt = ProcessingThread([], "test.xlsx")
            print("✓ ProcessingThread constructor signature OK")
        except Exception as e:
            issues.append(f"ProcessingThread constructor failed: {e}")
        
        # Test public methods exist and are callable
        public_methods = [
            'export_to_excel',  # Main public method
            'run',              # QThread method
        ]
        
        for method_name in public_methods:
            if hasattr(pt, method_name):
                method = getattr(pt, method_name)
                if callable(method):
                    print(f"✓ ProcessingThread.{method_name} is callable")
                    
                    # Check method signature
                    sig = inspect.signature(method)
                    print(f"  Signature: {method_name}{sig}")
                else:
                    issues.append(f"ProcessingThread.{method_name} is not callable")
            else:
                issues.append(f"ProcessingThread missing {method_name}")
        
        # Test that new private methods don't interfere with public API
        private_methods = [
            '_export_to_excel_optimized',
            '_export_to_excel_openpyxl',
            '_validate_results_data_integrity',
            '_verify_excel_file_integrity'
        ]
        
        for method_name in private_methods:
            if hasattr(pt, method_name):
                print(f"✓ ProcessingThread.{method_name} exists (private)")
            else:
                issues.append(f"ProcessingThread missing private method {method_name}")
        
        # Test that export_to_excel still works as expected (no parameters)
        try:
            sig = inspect.signature(pt.export_to_excel)
            if len(sig.parameters) == 0:
                print("✓ ProcessingThread.export_to_excel takes no parameters (API compatible)")
            else:
                issues.append(f"ProcessingThread.export_to_excel signature changed: {sig}")
        except Exception as e:
            issues.append(f"ProcessingThread.export_to_excel signature check failed: {e}")
        
    except Exception as e:
        issues.append(f"ProcessingThread API test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_threaded_processing_thread_api():
    """Test ThreadedProcessingThread public API compatibility"""
    print("\n=== TESTING THREADEDPROCESSINGTHREAD API COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import ThreadedProcessingThread
        
        # Test constructor signature
        try:
            tpt = ThreadedProcessingThread([], "test.xlsx")
            print("✓ ThreadedProcessingThread constructor signature OK")
        except Exception as e:
            issues.append(f"ThreadedProcessingThread constructor failed: {e}")
        
        # Test public methods exist and are callable
        public_methods = [
            'export_to_excel',  # Main public method
            'run',              # QThread method
        ]
        
        for method_name in public_methods:
            if hasattr(tpt, method_name):
                method = getattr(tpt, method_name)
                if callable(method):
                    print(f"✓ ThreadedProcessingThread.{method_name} is callable")
                    
                    # Check method signature
                    sig = inspect.signature(method)
                    print(f"  Signature: {method_name}{sig}")
                else:
                    issues.append(f"ThreadedProcessingThread.{method_name} is not callable")
            else:
                issues.append(f"ThreadedProcessingThread missing {method_name}")
        
        # Test that new private methods don't interfere with public API
        private_methods = [
            '_export_to_excel_optimized',
            '_export_to_excel_openpyxl',
            '_validate_results_data_integrity',
            '_verify_excel_file_integrity'
        ]
        
        for method_name in private_methods:
            if hasattr(tpt, method_name):
                print(f"✓ ThreadedProcessingThread.{method_name} exists (private)")
            else:
                issues.append(f"ThreadedProcessingThread missing private method {method_name}")
        
        # Test that export_to_excel still works as expected (no parameters)
        try:
            sig = inspect.signature(tpt.export_to_excel)
            if len(sig.parameters) == 0:
                print("✓ ThreadedProcessingThread.export_to_excel takes no parameters (API compatible)")
            else:
                issues.append(f"ThreadedProcessingThread.export_to_excel signature changed: {sig}")
        except Exception as e:
            issues.append(f"ThreadedProcessingThread.export_to_excel signature check failed: {e}")
        
    except Exception as e:
        issues.append(f"ThreadedProcessingThread API test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_email_parser_api():
    """Test EmailParser API compatibility"""
    print("\n=== TESTING EMAILPARSER API COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import EmailParser
        
        # Test constructor
        try:
            parser = EmailParser()
            print("✓ EmailParser constructor signature OK")
        except Exception as e:
            issues.append(f"EmailParser constructor failed: {e}")
        
        # Test sanitize_excel_value method (critical for compatibility)
        if hasattr(parser, 'sanitize_excel_value'):
            method = getattr(parser, 'sanitize_excel_value')
            if callable(method):
                sig = inspect.signature(method)
                print(f"✓ EmailParser.sanitize_excel_value signature: {sig}")
                
                # Test that it still works
                try:
                    result = parser.sanitize_excel_value("test string")
                    if isinstance(result, str):
                        print("✓ EmailParser.sanitize_excel_value returns string")
                    else:
                        issues.append(f"EmailParser.sanitize_excel_value returns {type(result)}, expected str")
                except Exception as e:
                    issues.append(f"EmailParser.sanitize_excel_value failed: {e}")
            else:
                issues.append("EmailParser.sanitize_excel_value is not callable")
        else:
            issues.append("EmailParser missing sanitize_excel_value method")
        
    except Exception as e:
        issues.append(f"EmailParser API test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_signal_compatibility():
    """Test that PyQt signals remain unchanged"""
    print("\n=== TESTING SIGNAL COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Expected signals for ProcessingThread
        expected_signals = [
            'progress_updated',
            'status_updated', 
            'file_processed',
            'finished_processing',
            'error_occurred'
        ]
        
        # Test ProcessingThread signals
        pt = ProcessingThread([], "test.xlsx")
        for signal_name in expected_signals:
            if hasattr(pt, signal_name):
                signal = getattr(pt, signal_name)
                print(f"✓ ProcessingThread.{signal_name} exists")
            else:
                issues.append(f"ProcessingThread missing signal {signal_name}")
        
        # Test ThreadedProcessingThread signals
        tpt = ThreadedProcessingThread([], "test.xlsx")
        for signal_name in expected_signals:
            if hasattr(tpt, signal_name):
                signal = getattr(tpt, signal_name)
                print(f"✓ ThreadedProcessingThread.{signal_name} exists")
            else:
                issues.append(f"ThreadedProcessingThread missing signal {signal_name}")
        
    except Exception as e:
        issues.append(f"Signal compatibility test failed: {e}")
        traceback.print_exc()
    
    return issues

def test_backward_compatibility():
    """Test that existing code patterns still work"""
    print("\n=== TESTING BACKWARD COMPATIBILITY ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Test typical usage pattern
        file_paths = ["test1.eml", "test2.eml"]
        output_path = "output.xlsx"
        
        # This is how the class would typically be used
        pt = ProcessingThread(file_paths, output_path)
        
        # Test that results can be set (typical usage)
        pt.results = [
            {'filename': 'test1.eml', 'subject': 'Test 1'},
            {'filename': 'test2.eml', 'subject': 'Test 2'}
        ]
        
        # Test that export_to_excel can be called (without actually running it)
        if callable(pt.export_to_excel):
            print("✓ Typical usage pattern works")
        else:
            issues.append("Typical usage pattern broken")
        
        # Test that the class can be used in the same way as before
        # (constructor parameters, attribute access, method calls)
        if hasattr(pt, 'file_paths') and hasattr(pt, 'output_path') and hasattr(pt, 'results'):
            print("✓ Expected attributes exist")
        else:
            issues.append("Expected attributes missing")
        
    except Exception as e:
        issues.append(f"Backward compatibility test failed: {e}")
        traceback.print_exc()
    
    return issues

def main():
    """Run API compatibility tests"""
    print("🔌 API COMPATIBILITY TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_processing_thread_api,
        test_threaded_processing_thread_api,
        test_email_parser_api,
        test_signal_compatibility,
        test_backward_compatibility,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ API COMPATIBILITY ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ ALL API COMPATIBILITY TESTS PASSED!")
        print("🎉 The pandas+xlsxwriter replacement maintains full API compatibility!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
