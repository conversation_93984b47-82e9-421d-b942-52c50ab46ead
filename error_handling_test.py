#!/usr/bin/env python3
"""
Test error handling in various edge cases
"""

import sys
import os
import tempfile
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_invalid_output_path():
    """Test handling of invalid output paths"""
    print("=== TESTING INVALID OUTPUT PATH ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Test with invalid directory
        invalid_path = "/nonexistent/directory/test.xlsx"
        
        pt = ProcessingThread([], invalid_path)
        pt.results = [{'filename': 'test.eml', 'subject': 'Test'}]
        
        # This should handle the error gracefully
        try:
            result = pt._export_to_excel_optimized()
            print("✓ Invalid path handled gracefully by optimized method")
        except Exception as e:
            # Should fall back to openpyxl method
            print(f"⚠️ Optimized method failed as expected: {e}")
            try:
                result = pt._export_to_excel_openpyxl()
                print("✓ Fallback method also handled invalid path gracefully")
            except Exception as e2:
                print(f"⚠️ Fallback method also failed as expected: {e2}")
        
    except Exception as e:
        issues.append(f"Invalid output path test failed: {e}")
    
    return issues

def test_empty_results():
    """Test handling of empty results"""
    print("\n=== TESTING EMPTY RESULTS ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Test ProcessingThread with empty results
            pt = ProcessingThread([], output_path)
            pt.results = []
            
            try:
                result = pt._export_to_excel_optimized()
                if os.path.exists(output_path):
                    print("✓ ProcessingThread optimized method handled empty results")
                else:
                    issues.append("ProcessingThread optimized method failed with empty results")
            except Exception as e:
                issues.append(f"ProcessingThread optimized method crashed with empty results: {e}")
            
            # Test ThreadedProcessingThread with empty results
            tpt = ThreadedProcessingThread([], output_path)
            tpt.results = []
            
            try:
                result = tpt._export_to_excel_optimized()
                if os.path.exists(output_path):
                    print("✓ ThreadedProcessingThread optimized method handled empty results")
                else:
                    issues.append("ThreadedProcessingThread optimized method failed with empty results")
            except Exception as e:
                issues.append(f"ThreadedProcessingThread optimized method crashed with empty results: {e}")
        
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Empty results test failed: {e}")
    
    return issues

def test_malformed_data():
    """Test handling of malformed data"""
    print("\n=== TESTING MALFORMED DATA ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Test with malformed data
            malformed_data = [
                {'filename': None, 'subject': 123, 'body': ['not', 'a', 'string']},
                {'missing_required_fields': True},
                {'filename': 'test.eml', 'subject': 'Test', 'body': '\x00\x01\x02\x03\x04\x05'}
            ]
            
            pt = ProcessingThread([], output_path)
            pt.results = malformed_data
            
            try:
                result = pt._export_to_excel_optimized()
                if os.path.exists(output_path):
                    print("✓ Optimized method handled malformed data")
                    
                    # Check if file is readable
                    import pandas as pd
                    df = pd.read_excel(output_path)
                    print(f"✓ Malformed data file readable: {df.shape}")
                else:
                    issues.append("Optimized method failed with malformed data")
            except Exception as e:
                print(f"⚠️ Optimized method failed with malformed data: {e}")
                # Try fallback
                try:
                    result = pt._export_to_excel_openpyxl()
                    if os.path.exists(output_path):
                        print("✓ Fallback method handled malformed data")
                    else:
                        issues.append("Both methods failed with malformed data")
                except Exception as e2:
                    issues.append(f"Both methods failed with malformed data: {e2}")
        
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Malformed data test failed: {e}")
    
    return issues

def test_large_data():
    """Test handling of large datasets"""
    print("\n=== TESTING LARGE DATA ===")
    
    issues = []
    
    try:
        from email_processor import ProcessingThread
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Create large dataset
            large_data = []
            for i in range(100):  # 100 records with long content
                record = {
                    'filename': f'email_{i:04d}.eml',
                    'file_path': f'/path/to/email_{i:04d}.eml',
                    'subject': f'Test Subject {i}',
                    'from_email': f'sender{i}@example.com',
                    'from_name': f'Sender {i}',
                    'from_full': f'Sender {i} <sender{i}@example.com>',
                    'to_emails': f'recipient{i}@example.com',
                    'to_names': f'Recipient {i}',
                    'to_full': f'Recipient {i} <recipient{i}@example.com>',
                    'cc_emails': '',
                    'cc_names': '',
                    'cc_full': '',
                    'bcc_emails': '',
                    'bcc_names': '',
                    'bcc_full': '',
                    'date': f'2024-01-{(i % 28) + 1:02d} 10:00:00',
                    'message_id': f'<msg{i}@example.com>',
                    'reply_to': '',
                    'priority': 'Normal',
                    'importance': 'Normal',
                    'content_type': 'text/plain',
                    'body': f'Very long body content for email {i}. ' * 1000,  # Very long content
                    'attachments_count': 0,
                    'attachments_names': '',
                    'attachments_sizes': '',
                    'total_attachments_size': 0,
                    'archive_file_list': '',
                    'archive_file_count': 0,
                    'archive_total_size': 0,
                    'ocr_extracted_text': '',
                    'document_content': '',
                    'attachment_processing_status': '',
                    'attachment_processing_time': 0,
                    'embedded_image_ocr_text': '',
                    'embedded_images_count': 0,
                    'embedded_images_info': '',
                    'encoding_detected': 'utf-8',
                    'has_html': False,
                    'error': ''
                }
                large_data.append(record)
            
            pt = ProcessingThread([], output_path)
            pt.results = large_data
            
            import time
            start_time = time.time()
            
            try:
                result = pt._export_to_excel_optimized()
                end_time = time.time()
                
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    processing_time = end_time - start_time
                    print(f"✓ Large data handled: {file_size:,} bytes in {processing_time:.2f}s")
                    
                    # Verify file integrity
                    import pandas as pd
                    df = pd.read_excel(output_path)
                    if len(df) == len(large_data):
                        print("✓ All large data records exported correctly")
                    else:
                        issues.append(f"Large data record count mismatch: expected {len(large_data)}, got {len(df)}")
                else:
                    issues.append("Large data export failed to create file")
            except Exception as e:
                issues.append(f"Large data export failed: {e}")
        
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    except Exception as e:
        issues.append(f"Large data test failed: {e}")
    
    return issues

def test_dependency_unavailable():
    """Test behavior when dependencies are unavailable"""
    print("\n=== TESTING DEPENDENCY UNAVAILABLE SCENARIO ===")
    
    issues = []
    
    try:
        # This is a conceptual test - we can't actually remove pandas/xlsxwriter
        # but we can test the logic
        from email_processor import ProcessingThread
        
        pt = ProcessingThread([], "test.xlsx")
        pt.results = [{'filename': 'test.eml', 'subject': 'Test'}]
        
        # Check that the dependency flags exist
        from email_processor import PANDAS_AVAILABLE, XLSXWRITER_AVAILABLE
        
        if PANDAS_AVAILABLE and XLSXWRITER_AVAILABLE:
            print("✓ Dependencies available - optimized method will be used")
        else:
            print("⚠️ Dependencies unavailable - fallback method will be used")
            if not PANDAS_AVAILABLE:
                print("  - pandas not available")
            if not XLSXWRITER_AVAILABLE:
                print("  - xlsxwriter not available")
        
        # The actual fallback logic is tested in the optimized method
        print("✓ Dependency checking logic is in place")
        
    except Exception as e:
        issues.append(f"Dependency unavailable test failed: {e}")
    
    return issues

def main():
    """Run error handling tests"""
    print("🚨 ERROR HANDLING TEST")
    print("=" * 60)
    
    all_issues = []
    
    # Run all tests
    tests = [
        test_invalid_output_path,
        test_empty_results,
        test_malformed_data,
        test_large_data,
        test_dependency_unavailable,
    ]
    
    for test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"Test {test_func.__name__} crashed: {e}")
            traceback.print_exc()
    
    # Report results
    print("\n" + "=" * 60)
    if all_issues:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False
    else:
        print("✅ ALL ERROR HANDLING TESTS PASSED!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
